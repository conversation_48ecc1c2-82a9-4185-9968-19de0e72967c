groups:
  - name: soak-test-monitoring
    rules:
      # Authority job monitoring
      - alert: AuthorityJobMissed
        expr: time() - authority_job_last_success_timestamp > 86400  # 24 hours
        for: 1h
        labels:
          severity: critical
          test_phase: soak
        annotations:
          summary: "Authority job missed during soak test"
          description: "Nightly authority job hasn't run successfully in over 24 hours during soak test."

      - alert: AuthorityJobFailure
        expr: increase(authority_job_failures_total[1h]) > 0
        for: 0m
        labels:
          severity: critical
          test_phase: soak
        annotations:
          summary: "Authority job failed during soak test"
          description: "Authority calculation job failed during 48h soak test period."

      # Performance degradation during soak test
      - alert: SoakTestPerformanceDegradation
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1.0
        for: 10m
        labels:
          severity: warning
          test_phase: soak
        annotations:
          summary: "Performance degradation during soak test"
          description: "P95 response time exceeded 1s for 10+ minutes during soak test."

      # Memory usage monitoring
      - alert: SoakTestMemoryLeak
        expr: increase(container_memory_usage_bytes[1h]) > 100000000  # 100MB increase per hour
        for: 2h
        labels:
          severity: warning
          test_phase: soak
        annotations:
          summary: "Potential memory leak during soak test"
          description: "Memory usage increasing consistently during soak test."

      # Security alert spike
      - alert: SoakTestSecurityAlertSpike
        expr: increase(security_alerts_total[1h]) > 10
        for: 0m
        labels:
          severity: warning
          test_phase: soak
        annotations:
          summary: "Security alert spike during soak test"
          description: "Unusual number of security alerts during soak test period."
