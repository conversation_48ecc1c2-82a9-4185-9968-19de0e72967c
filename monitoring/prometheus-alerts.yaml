# Prometheus Alert Rules for Legal Database API Security Monitoring
# Deploy to Google Cloud Monitoring or Prometheus instance

groups:
  - name: legal-database-security
    rules:
      # Rate limiting alerts - detect credential stuffing
      - alert: HighRateLimitBlocks
        expr: increase(rate_limit_block_total[5m]) > 50
        for: 2m
        labels:
          severity: warning
          service: legal-database-api
          category: security
        annotations:
          summary: "High rate limit blocks detected"
          description: "{{ $value }} rate limit blocks in the last 5 minutes. Possible credential stuffing attack."
          runbook_url: "https://docs.legal-database.com/runbooks/rate-limit-blocks"

      - alert: CriticalRateLimitBlocks
        expr: increase(rate_limit_block_total[5m]) > 200
        for: 1m
        labels:
          severity: critical
          service: legal-database-api
          category: security
        annotations:
          summary: "Critical rate limit blocks - possible DDoS"
          description: "{{ $value }} rate limit blocks in the last 5 minutes. Immediate investigation required."
          runbook_url: "https://docs.legal-database.com/runbooks/ddos-response"

      # Authentication failure alerts
      - alert: HighAuthFailures
        expr: increase(jwt_validation_failures_total[10m]) > 100
        for: 3m
        labels:
          severity: warning
          service: legal-database-api
          category: security
        annotations:
          summary: "High JWT validation failures"
          description: "{{ $value }} JWT validation failures in the last 10 minutes."

      # Suspicious activity alerts
      - alert: SuspiciousRequestPatterns
        expr: increase(security_suspicious_requests_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          service: legal-database-api
          category: security
        annotations:
          summary: "Suspicious request patterns detected"
          description: "{{ $value }} suspicious requests detected (XSS, SQL injection, path traversal)."

      # Direct Cloud Run access attempts (should be 0)
      - alert: DirectCloudRunAccess
        expr: increase(direct_access_attempts_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
          service: legal-database-api
          category: security
        annotations:
          summary: "Direct Cloud Run access attempted"
          description: "Someone is trying to bypass the API Gateway. Immediate investigation required."

      # Service availability
      - alert: APIGatewayDown
        expr: up{job="api-gateway"} == 0
        for: 1m
        labels:
          severity: critical
          service: legal-database-api
          category: availability
        annotations:
          summary: "API Gateway is down"
          description: "API Gateway has been down for more than 1 minute."

      - alert: CloudRunDown
        expr: up{job="cloud-run"} == 0
        for: 1m
        labels:
          severity: critical
          service: legal-database-api
          category: availability
        annotations:
          summary: "Cloud Run service is down"
          description: "Cloud Run service has been down for more than 1 minute."

      # Performance alerts
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2.0
        for: 5m
        labels:
          severity: warning
          service: legal-database-api
          category: performance
        annotations:
          summary: "High response times detected"
          description: "95th percentile response time is {{ $value }}s over the last 5 minutes."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          service: legal-database-api
          category: reliability
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} over the last 5 minutes."

  - name: legal-database-business
    rules:
      # Business logic alerts
      - alert: LowSearchActivity
        expr: rate(search_requests_total[1h]) < 0.1
        for: 30m
        labels:
          severity: info
          service: legal-database-api
          category: business
        annotations:
          summary: "Unusually low search activity"
          description: "Search request rate is {{ $value }} per second over the last hour."

      - alert: HighCacheHitRate
        expr: rate(cache_hits_total[5m]) / rate(cache_requests_total[5m]) > 0.95
        for: 10m
        labels:
          severity: info
          service: legal-database-api
          category: performance
        annotations:
          summary: "Very high cache hit rate"
          description: "Cache hit rate is {{ $value | humanizePercentage }}. Consider cache tuning."

  - name: legal-database-infrastructure
    rules:
      # Infrastructure alerts
      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: legal-database-api
          category: infrastructure
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }} of limit."

      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
        for: 10m
        labels:
          severity: warning
          service: legal-database-api
          category: infrastructure
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value | humanizePercentage }} over the last 5 minutes."

      # Database connection alerts
      - alert: DatabaseConnectionFailures
        expr: increase(database_connection_failures_total[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: legal-database-api
          category: database
        annotations:
          summary: "Database connection failures"
          description: "{{ $value }} database connection failures in the last 5 minutes."

  - name: legal-database-compliance
    rules:
      # SOC 2 compliance alerts
      - alert: UnauthorizedAdminAccess
        expr: increase(admin_access_denied_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
          service: legal-database-api
          category: compliance
        annotations:
          summary: "Unauthorized admin access attempt"
          description: "{{ $value }} unauthorized admin access attempts detected."

      - alert: DataExportActivity
        expr: increase(data_export_requests_total[1h]) > 10
        for: 0m
        labels:
          severity: info
          service: legal-database-api
          category: compliance
        annotations:
          summary: "High data export activity"
          description: "{{ $value }} data export requests in the last hour."

      # Audit trail alerts
      - alert: AuditLogFailures
        expr: increase(audit_log_failures_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
          service: legal-database-api
          category: compliance
        annotations:
          summary: "Audit log failures detected"
          description: "{{ $value }} audit log write failures. Compliance at risk."
