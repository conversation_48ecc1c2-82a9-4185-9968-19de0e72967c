{"displayName": "Legal API - 48h Soak Test", "mosaicLayout": {"tiles": [{"width": 6, "height": 4, "widget": {"title": "Authority Job Success Rate", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_function\" AND resource.labels.function_name=\"authority-calculator\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM"}}}}]}}}, {"width": 6, "height": 4, "widget": {"title": "Security <PERSON><PERSON>", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND metric.type=\"logging.googleapis.com/user/security_alerts\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_RATE"}}}}]}}}, {"width": 12, "height": 4, "widget": {"title": "API Response Times (P95)", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/request_latencies\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_DELTA", "crossSeriesReducer": "REDUCE_PERCENTILE_95"}}}}]}}}]}}