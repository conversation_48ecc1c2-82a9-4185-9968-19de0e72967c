import json
import time
import requests
from google.cloud import logging
import functions_framework

# Configure logging
client = logging.Client()
logger = client.logger('soak-test-monitor')

@functions_framework.http
def monitor_soak_test(request):
    """Detailed monitoring function for soak test."""
    
    gateway_url = "https://legal-database-gateway-1k6gjpoj.uc.gateway.dev"
    
    results = {
        "timestamp": time.time(),
        "checks": {}
    }
    
    # Test health endpoint
    try:
        start_time = time.time()
        response = requests.get(f"{gateway_url}/health", timeout=10)
        end_time = time.time()
        
        results["checks"]["health"] = {
            "status_code": response.status_code,
            "response_time": end_time - start_time,
            "success": response.status_code == 200
        }
        
        logger.log_struct({
            "test": "health_check",
            "status_code": response.status_code,
            "response_time": end_time - start_time,
            "timestamp": time.time()
        })
        
    except Exception as e:
        results["checks"]["health"] = {
            "error": str(e),
            "success": False
        }
        logger.log_struct({
            "test": "health_check",
            "error": str(e),
            "timestamp": time.time()
        })
    
    # Test sample endpoint
    try:
        start_time = time.time()
        response = requests.get(f"{gateway_url}/v0/graph/sample", timeout=10)
        end_time = time.time()
        
        results["checks"]["sample"] = {
            "status_code": response.status_code,
            "response_time": end_time - start_time,
            "success": response.status_code == 200
        }
        
        logger.log_struct({
            "test": "sample_endpoint",
            "status_code": response.status_code,
            "response_time": end_time - start_time,
            "timestamp": time.time()
        })
        
    except Exception as e:
        results["checks"]["sample"] = {
            "error": str(e),
            "success": False
        }
    
    # Test security (protected endpoint should be 401/404)
    try:
        response = requests.get(f"{gateway_url}/v0/search?q=test", timeout=10)
        
        results["checks"]["security"] = {
            "status_code": response.status_code,
            "success": response.status_code in [401, 404]
        }
        
        logger.log_struct({
            "test": "security_check",
            "status_code": response.status_code,
            "success": response.status_code in [401, 404],
            "timestamp": time.time()
        })
        
    except Exception as e:
        results["checks"]["security"] = {
            "error": str(e),
            "success": False
        }
    
    return json.dumps(results), 200
