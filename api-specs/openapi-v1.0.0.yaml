swagger: '2.0'
info:
  title: Legal Database API
  description: |
    **Production-ready Legal Database API v1.0.0**
    
    This API provides access to legal document search, recommendations, and citation network analysis.
    
    ## Authentication
    All protected endpoints require a valid JWT token from Supa<PERSON> in the Authorization header:
    ```
    Authorization: Bearer <jwt_token>
    ```
    
    ## Rate Limiting
    - Public endpoints: 1000 requests/hour
    - Authenticated endpoints: 10000 requests/hour
    
    ## Deprecation Notice
    - `/v0/*` endpoints are deprecated and will be removed in v2.0.0
    - Use `/v1/*` endpoints for new integrations
    
  version: 1.0.0
  contact:
    name: Legal Database API Support
    email: <EMAIL>
  license:
    name: Proprietary
host: legal-api.ailex.com
schemes:
  - https
produces:
  - application/json
consumes:
  - application/json

# Security definitions for JWT authentication
securityDefinitions:
  jwt_auth:
    type: apiKey
    name: Authorization
    in: header
    x-google-issuer: "https://anwefmklplkjxkmzpnva.supabase.co/auth/v1"
    x-google-jwks_uri: "https://anwefmklplkjxkmzpnva.supabase.co/auth/v1/keys"
    x-google-audiences: "anwefmklplkjxkmzpnva"

paths:
  # Public endpoints
  /:
    get:
      summary: API Information
      description: Returns API version and available endpoints
      operationId: api_root
      tags: [System]
      responses:
        200:
          description: API information
          schema:
            $ref: '#/definitions/ApiInfo'
      x-google-backend:
        address: https://legal-api-prod.run.app/

  /health:
    get:
      summary: Health Check
      description: Returns service health status
      operationId: health_check
      tags: [System]
      responses:
        200:
          description: Service is healthy
          schema:
            $ref: '#/definitions/HealthStatus'
      x-google-backend:
        address: https://legal-api-prod.run.app/health

  # V1 Endpoints (Current)
  /v1/search:
    get:
      summary: Search Legal Documents
      description: |
        Search legal documents using hybrid semantic and keyword search.
        Supports filtering by jurisdiction, practice area, and document type.
      operationId: search_documents_v1
      tags: [Search]
      security:
        - jwt_auth: []
      parameters:
        - name: q
          in: query
          type: string
          required: true
          description: Search query
          example: "personal injury statute of limitations"
        - name: limit
          in: query
          type: integer
          default: 10
          minimum: 1
          maximum: 100
          description: Maximum number of results
        - name: offset
          in: query
          type: integer
          default: 0
          minimum: 0
          description: Offset for pagination
        - name: jurisdiction
          in: query
          type: string
          description: Filter by jurisdiction (e.g., "TX", "NY")
        - name: practice_area
          in: query
          type: string
          description: Filter by practice area
          enum: [personal_injury, criminal_defense, family_law, corporate]
      responses:
        200:
          description: Search results
          schema:
            $ref: '#/definitions/SearchResults'
        400:
          description: Invalid request parameters
        401:
          description: Authentication required
        429:
          description: Rate limit exceeded
      x-google-backend:
        address: https://legal-api-prod.run.app/v1/search

  /v1/recommend/{document_id}:
    get:
      summary: Get Document Recommendations
      description: |
        Get related documents based on citation analysis and semantic similarity.
        Uses PageRank authority scores and recency weighting.
      operationId: get_recommendations_v1
      tags: [Recommendations]
      security:
        - jwt_auth: []
      parameters:
        - name: document_id
          in: path
          type: string
          required: true
          description: Document ID
          example: "C-2023-TX-123"
        - name: limit
          in: query
          type: integer
          default: 10
          minimum: 1
          maximum: 50
          description: Maximum number of recommendations
        - name: include_score_explanation
          in: query
          type: boolean
          default: false
          description: Include scoring explanation for transparency
      responses:
        200:
          description: Document recommendations
          schema:
            $ref: '#/definitions/RecommendationResults'
        401:
          description: Authentication required
        404:
          description: Document not found
        429:
          description: Rate limit exceeded
      x-google-backend:
        address: https://legal-api-prod.run.app/v1/recommend/{document_id}

  /v1/graph:
    get:
      summary: Get Citation Network Graph
      description: |
        Get React-Flow compatible citation network data for visualization.
        Supports depth control and node type filtering.
      operationId: get_graph_v1
      tags: [Graph]
      security:
        - jwt_auth: []
      parameters:
        - name: id
          in: query
          type: string
          required: true
          description: Starting document ID
          example: "C-2023-TX-123"
        - name: depth
          in: query
          type: integer
          default: 2
          minimum: 1
          maximum: 3
          description: Graph traversal depth
        - name: direction
          in: query
          type: string
          default: both
          enum: [both, in, out]
          description: Citation direction (both, incoming, outgoing)
        - name: max_nodes
          in: query
          type: integer
          default: 200
          minimum: 10
          maximum: 500
          description: Maximum number of nodes
        - name: node_types
          in: query
          type: array
          items:
            type: string
            enum: [case, statute, regulation, opinion]
          description: Filter by node types
      responses:
        200:
          description: React-Flow compatible graph data
          schema:
            $ref: '#/definitions/GraphData'
        400:
          description: Invalid parameters
        401:
          description: Authentication required
        429:
          description: Rate limit exceeded
      x-google-backend:
        address: https://legal-api-prod.run.app/v1/graph

  # Sample/Demo endpoints (public)
  /v1/graph/sample:
    get:
      summary: Sample Graph Data
      description: Returns sample React-Flow compatible graph data for testing
      operationId: sample_graph_v1
      tags: [Graph, Demo]
      responses:
        200:
          description: Sample graph data
          schema:
            $ref: '#/definitions/GraphData'
      x-google-backend:
        address: https://legal-api-prod.run.app/v1/graph/sample

  # V0 Endpoints (Deprecated)
  /v0/search:
    get:
      summary: "[DEPRECATED] Search Documents"
      description: |
        **DEPRECATED**: Use `/v1/search` instead.
        This endpoint will be removed in v2.0.0.
      operationId: search_documents_v0
      tags: [Search, Deprecated]
      deprecated: true
      security:
        - jwt_auth: []
      parameters:
        - name: q
          in: query
          type: string
          required: true
          description: Search query
        - name: limit
          in: query
          type: integer
          default: 10
          description: Maximum number of results
        - name: offset
          in: query
          type: integer
          default: 0
          description: Offset for pagination
      responses:
        200:
          description: Search results
          schema:
            $ref: '#/definitions/SearchResults'
        401:
          description: Authentication required
      x-google-backend:
        address: https://legal-api-prod.run.app/v0/search

  /v0/graph:
    get:
      summary: "[DEPRECATED] Get Graph Data"
      description: |
        **DEPRECATED**: Use `/v1/graph` instead.
        This endpoint will be removed in v2.0.0.
      operationId: get_graph_v0
      tags: [Graph, Deprecated]
      deprecated: true
      security:
        - jwt_auth: []
      parameters:
        - name: id
          in: query
          type: string
          required: true
          description: Starting document ID
        - name: depth
          in: query
          type: integer
          default: 2
          description: Graph traversal depth
        - name: max_nodes
          in: query
          type: integer
          default: 200
          description: Maximum number of nodes
      responses:
        200:
          description: Graph data
          schema:
            $ref: '#/definitions/GraphData'
        401:
          description: Authentication required
      x-google-backend:
        address: https://legal-api-prod.run.app/v0/graph

  # Admin endpoints
  /admin/authority/calculate:
    post:
      summary: Trigger Authority Calculation
      description: |
        Manually trigger PageRank authority score calculation.
        Requires admin privileges.
      operationId: trigger_authority_calc
      tags: [Admin]
      security:
        - jwt_auth: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/AuthorityCalculationRequest'
      responses:
        200:
          description: Authority calculation triggered
          schema:
            $ref: '#/definitions/JobStatus'
        401:
          description: Authentication required
        403:
          description: Admin privileges required
        500:
          description: Internal server error
      x-google-backend:
        address: https://legal-api-prod.run.app/admin/authority/calculate

# Data models
definitions:
  ApiInfo:
    type: object
    properties:
      name:
        type: string
        example: "Legal Database API"
      version:
        type: string
        example: "1.0.0"
      description:
        type: string
      endpoints:
        type: object
      features:
        type: array
        items:
          type: string

  HealthStatus:
    type: object
    properties:
      status:
        type: string
        enum: [healthy, unhealthy]
      timestamp:
        type: string
        format: date-time
      version:
        type: string
      checks:
        type: object

  SearchResults:
    type: object
    properties:
      query:
        type: string
      total:
        type: integer
      limit:
        type: integer
      offset:
        type: integer
      results:
        type: array
        items:
          $ref: '#/definitions/Document'
      facets:
        type: object
      cache_hit:
        type: boolean

  Document:
    type: object
    properties:
      id:
        type: string
      title:
        type: string
      content:
        type: string
      jurisdiction:
        type: string
      practice_area:
        type: string
      document_type:
        type: string
      date:
        type: string
        format: date
      authority_score:
        type: number
        format: float
      relevance_score:
        type: number
        format: float

  RecommendationResults:
    type: object
    properties:
      document_id:
        type: string
      recommendations:
        type: array
        items:
          $ref: '#/definitions/Recommendation'
      total:
        type: integer
      cache_hit:
        type: boolean

  Recommendation:
    type: object
    properties:
      document:
        $ref: '#/definitions/Document'
      score:
        type: number
        format: float
      explanation:
        type: string
      relationship_type:
        type: string
        enum: [citation, semantic_similarity, authority_based]

  GraphData:
    type: object
    properties:
      nodes:
        type: array
        items:
          $ref: '#/definitions/GraphNode'
      edges:
        type: array
        items:
          $ref: '#/definitions/GraphEdge'
      metadata:
        $ref: '#/definitions/GraphMetadata'

  GraphNode:
    type: object
    properties:
      id:
        type: string
      type:
        type: string
      data:
        type: object
      position:
        type: object

  GraphEdge:
    type: object
    properties:
      id:
        type: string
      source:
        type: string
      target:
        type: string
      type:
        type: string
      data:
        type: object

  GraphMetadata:
    type: object
    properties:
      node_count:
        type: integer
      edge_count:
        type: integer
      depth:
        type: integer
      cache_hit:
        type: boolean

  AuthorityCalculationRequest:
    type: object
    properties:
      force:
        type: boolean
        default: false
        description: Force calculation even if recent
      dry_run:
        type: boolean
        default: false
        description: Dry run without actual calculation

  JobStatus:
    type: object
    properties:
      job_id:
        type: string
      status:
        type: string
        enum: [queued, running, completed, failed]
      message:
        type: string
      started_at:
        type: string
        format: date-time
      completed_at:
        type: string
        format: date-time

tags:
  - name: System
    description: System health and information endpoints
  - name: Search
    description: Document search functionality
  - name: Recommendations
    description: Document recommendation engine
  - name: Graph
    description: Citation network visualization
  - name: Admin
    description: Administrative functions
  - name: Demo
    description: Demo and sample endpoints
  - name: Deprecated
    description: Deprecated endpoints (will be removed)
