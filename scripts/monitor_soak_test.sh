#!/bin/bash
# Continuous monitoring script for soak test

GATEWAY_URL="https://legal-database-gateway-1k6gjpoj.uc.gateway.dev"
LOG_FILE="soak_test_monitoring.log"

while true; do
    TIMESTAMP=$(date)
    
    # Test health endpoint
    HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}:%{time_total}" "$GATEWAY_URL/health")
    HTTP_CODE=$(echo $HEALTH_STATUS | cut -d: -f1)
    RESPONSE_TIME=$(echo $HEALTH_STATUS | cut -d: -f2)
    
    # Log results
    echo "$TIMESTAMP - Health: $HTTP_CODE, Response: ${RESPONSE_TIME}s" >> "$LOG_FILE"
    
    # Test sample endpoint
    SAMPLE_STATUS=$(curl -s -o /dev/null -w "%{http_code}:%{time_total}" "$GATEWAY_URL/v0/graph/sample")
    SAMPLE_CODE=$(echo $SAMPLE_STATUS | cut -d: -f1)
    SAMPLE_TIME=$(echo $SAMPLE_STATUS | cut -d: -f2)
    
    echo "$TIMESTAMP - Sample: $SAMPLE_CODE, Response: ${SAMPLE_TIME}s" >> "$LOG_FILE"
    
    # Check if response time is acceptable (< 2 seconds for warning)
    if (( $(echo "$RESPONSE_TIME > 2.0" | bc -l) )); then
        echo "$TIMESTAMP - WARNING: High response time: ${RESPONSE_TIME}s" >> "$LOG_FILE"
    fi
    
    # Sleep for 5 minutes
    sleep 300
done
