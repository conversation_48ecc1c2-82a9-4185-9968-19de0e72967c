#!/bin/bash
# Setup Google Cloud-based monitoring for 48h soak test
# This runs in the cloud, independent of your local machine

set -e

# Configuration
PROJECT_ID="${PROJECT_ID:-texas-laws-personalinjury}"
REGION="${REGION:-us-central1}"
GATEWAY_URL="https://legal-database-gateway-1k6gjpoj.uc.gateway.dev"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🌐 Setting up Google Cloud-based Soak Test Monitoring${NC}"
echo "=================================================="

# 1. Create Cloud Scheduler job for continuous health checks
echo -e "${YELLOW}⏰ Creating Cloud Scheduler health check job...${NC}"

gcloud scheduler jobs create http soak-test-health-monitor \
    --location=$REGION \
    --schedule="*/5 * * * *" \
    --uri="$GATEWAY_URL/health" \
    --http-method=GET \
    --description="Soak test health check every 5 minutes" \
    --time-zone="UTC" \
    --attempt-deadline=30s \
    --max-retry-attempts=3 || echo "Job may already exist"

echo -e "${GREEN}✅ Health check job created (runs every 5 minutes)${NC}"

# 2. Create Cloud Scheduler job for sample endpoint monitoring
echo -e "${YELLOW}⏰ Creating sample endpoint monitoring job...${NC}"

gcloud scheduler jobs create http soak-test-sample-monitor \
    --location=$REGION \
    --schedule="*/10 * * * *" \
    --uri="$GATEWAY_URL/v0/graph/sample" \
    --http-method=GET \
    --description="Soak test sample endpoint check every 10 minutes" \
    --time-zone="UTC" \
    --attempt-deadline=30s \
    --max-retry-attempts=3 || echo "Job may already exist"

echo -e "${GREEN}✅ Sample endpoint monitoring created (runs every 10 minutes)${NC}"

# 3. Create uptime check in Cloud Monitoring
echo -e "${YELLOW}📊 Creating Cloud Monitoring uptime check...${NC}"

cat > uptime-check-config.json << EOF
{
  "displayName": "Legal API Gateway - Soak Test",
  "httpCheck": {
    "requestMethod": "GET",
    "useSsl": true,
    "path": "/health",
    "port": 443
  },
  "monitoredResource": {
    "type": "uptime_url",
    "labels": {
      "project_id": "$PROJECT_ID",
      "host": "legal-database-gateway-1k6gjpoj.uc.gateway.dev"
    }
  },
  "checkIntervalDuration": "300s",
  "timeout": "10s",
  "contentMatchers": [
    {
      "content": "healthy",
      "matcher": "CONTAINS_STRING"
    }
  ],
  "selectedRegions": [
    "USA",
    "EUROPE",
    "ASIA_PACIFIC"
  ]
}
EOF

# Create the uptime check
gcloud monitoring uptime create --config-from-file=uptime-check-config.json || echo "Uptime check may already exist"

echo -e "${GREEN}✅ Cloud Monitoring uptime check created${NC}"

# 4. Create alerting policy for soak test failures
echo -e "${YELLOW}🚨 Creating soak test alerting policy...${NC}"

cat > soak-test-alert-policy.json << EOF
{
  "displayName": "Soak Test - Service Down Alert",
  "conditions": [
    {
      "displayName": "Uptime check failure",
      "conditionThreshold": {
        "filter": "resource.type=\"uptime_url\"",
        "comparison": "COMPARISON_GREATER_THAN",
        "thresholdValue": 1,
        "duration": "300s",
        "aggregations": [
          {
            "alignmentPeriod": "300s",
            "perSeriesAligner": "ALIGN_RATE",
            "crossSeriesReducer": "REDUCE_COUNT_FALSE"
          }
        ]
      }
    }
  ],
  "alertStrategy": {
    "autoClose": "1800s"
  },
  "combiner": "OR",
  "enabled": true,
  "notificationChannels": []
}
EOF

gcloud alpha monitoring policies create --policy-from-file=soak-test-alert-policy.json || echo "Alert policy may already exist"

echo -e "${GREEN}✅ Soak test alerting policy created${NC}"

# 5. Create Cloud Function for detailed monitoring (optional)
echo -e "${YELLOW}🔧 Setting up detailed monitoring function...${NC}"

mkdir -p functions/soak-monitor
cat > functions/soak-monitor/main.py << 'EOF'
import json
import time
import requests
from google.cloud import logging
import functions_framework

# Configure logging
client = logging.Client()
logger = client.logger('soak-test-monitor')

@functions_framework.http
def monitor_soak_test(request):
    """Detailed monitoring function for soak test."""
    
    gateway_url = "https://legal-database-gateway-1k6gjpoj.uc.gateway.dev"
    
    results = {
        "timestamp": time.time(),
        "checks": {}
    }
    
    # Test health endpoint
    try:
        start_time = time.time()
        response = requests.get(f"{gateway_url}/health", timeout=10)
        end_time = time.time()
        
        results["checks"]["health"] = {
            "status_code": response.status_code,
            "response_time": end_time - start_time,
            "success": response.status_code == 200
        }
        
        logger.log_struct({
            "test": "health_check",
            "status_code": response.status_code,
            "response_time": end_time - start_time,
            "timestamp": time.time()
        })
        
    except Exception as e:
        results["checks"]["health"] = {
            "error": str(e),
            "success": False
        }
        logger.log_struct({
            "test": "health_check",
            "error": str(e),
            "timestamp": time.time()
        })
    
    # Test sample endpoint
    try:
        start_time = time.time()
        response = requests.get(f"{gateway_url}/v0/graph/sample", timeout=10)
        end_time = time.time()
        
        results["checks"]["sample"] = {
            "status_code": response.status_code,
            "response_time": end_time - start_time,
            "success": response.status_code == 200
        }
        
        logger.log_struct({
            "test": "sample_endpoint",
            "status_code": response.status_code,
            "response_time": end_time - start_time,
            "timestamp": time.time()
        })
        
    except Exception as e:
        results["checks"]["sample"] = {
            "error": str(e),
            "success": False
        }
    
    # Test security (protected endpoint should be 401/404)
    try:
        response = requests.get(f"{gateway_url}/v0/search?q=test", timeout=10)
        
        results["checks"]["security"] = {
            "status_code": response.status_code,
            "success": response.status_code in [401, 404]
        }
        
        logger.log_struct({
            "test": "security_check",
            "status_code": response.status_code,
            "success": response.status_code in [401, 404],
            "timestamp": time.time()
        })
        
    except Exception as e:
        results["checks"]["security"] = {
            "error": str(e),
            "success": False
        }
    
    return json.dumps(results), 200
EOF

cat > functions/soak-monitor/requirements.txt << 'EOF'
requests==2.31.0
google-cloud-logging==3.8.0
functions-framework==3.4.0
EOF

echo -e "${GREEN}✅ Monitoring function code created${NC}"

# Deploy the monitoring function
echo -e "${YELLOW}🚀 Deploying monitoring function...${NC}"

gcloud functions deploy soak-test-monitor \
    --gen2 \
    --runtime=python311 \
    --region=$REGION \
    --source=functions/soak-monitor \
    --entry-point=monitor_soak_test \
    --trigger=http \
    --allow-unauthenticated \
    --memory=256MB \
    --timeout=60s || echo "Function deployment may have failed - continuing..."

echo -e "${GREEN}✅ Monitoring function deployed${NC}"

# 6. Create Cloud Scheduler job to call the monitoring function
echo -e "${YELLOW}⏰ Creating detailed monitoring schedule...${NC}"

FUNCTION_URL="https://$REGION-$PROJECT_ID.cloudfunctions.net/soak-test-monitor"

gcloud scheduler jobs create http soak-test-detailed-monitor \
    --location=$REGION \
    --schedule="*/15 * * * *" \
    --uri="$FUNCTION_URL" \
    --http-method=GET \
    --description="Detailed soak test monitoring every 15 minutes" \
    --time-zone="UTC" \
    --attempt-deadline=60s || echo "Job may already exist"

echo -e "${GREEN}✅ Detailed monitoring scheduled (every 15 minutes)${NC}"

# Cleanup temporary files
rm -f uptime-check-config.json soak-test-alert-policy.json

echo ""
echo -e "${BLUE}🌐 Google Cloud Monitoring Setup Complete${NC}"
echo "========================================"
echo ""
echo -e "${GREEN}✅ Cloud-based monitoring is now active:${NC}"
echo "• Health checks: Every 5 minutes via Cloud Scheduler"
echo "• Sample endpoint: Every 10 minutes via Cloud Scheduler"
echo "• Uptime monitoring: Every 5 minutes via Cloud Monitoring"
echo "• Detailed monitoring: Every 15 minutes via Cloud Function"
echo "• Alerting: Automatic notifications for failures"
echo ""
echo -e "${YELLOW}📊 Monitor your soak test at:${NC}"
echo "• Cloud Scheduler: https://console.cloud.google.com/cloudscheduler"
echo "• Cloud Monitoring: https://console.cloud.google.com/monitoring"
echo "• Cloud Logging: https://console.cloud.google.com/logs"
echo ""
echo -e "${BLUE}🎯 This monitoring runs 24/7 in Google Cloud${NC}"
echo "• Independent of your local machine"
echo "• Continues even when your computer is off"
echo "• Provides comprehensive 48-hour validation"
echo ""
echo -e "${GREEN}🎉 Cloud-based soak test monitoring is now running!${NC}"
