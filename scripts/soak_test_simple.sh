#!/bin/bash
# Simplified 48-Hour Staging Soak Test
# Monitors staging environment for production readiness

set -e

# Configuration
PROJECT_ID="${PROJECT_ID:-texas-laws-personalinjury}"
REGION="${REGION:-us-central1}"
SERVICE_NAME="legal-api-stg"
GATEWAY_URL="https://legal-database-gateway-1k6gjpoj.uc.gateway.dev"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Starting 48-Hour Staging Soak Test${NC}"
echo "====================================="
echo "Start time: $(date)"
echo "End time: $(date -v+48H)"
echo ""

# Test current system health
echo -e "${YELLOW}🔍 Initial System Health Check...${NC}"

# Test staging gateway health
echo "Testing staging gateway health..."
GATEWAY_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" "$GATEWAY_URL/health")
if [ "$GATEWAY_HEALTH" = "200" ]; then
    echo -e "${GREEN}✅ Gateway health check passed (200)${NC}"
else
    echo -e "${RED}❌ Gateway health check failed (HTTP $GATEWAY_HEALTH)${NC}"
    exit 1
fi

# Test sample endpoint
echo "Testing sample graph endpoint..."
SAMPLE_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$GATEWAY_URL/v0/graph/sample")
if [ "$SAMPLE_RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Sample endpoint working (200)${NC}"
else
    echo -e "${RED}❌ Sample endpoint failed (HTTP $SAMPLE_RESPONSE)${NC}"
    exit 1
fi

# Test protected endpoint (should be 401/404 without auth)
echo "Testing protected endpoint security..."
PROTECTED_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$GATEWAY_URL/v0/search?q=test")
if [ "$PROTECTED_RESPONSE" = "401" ] || [ "$PROTECTED_RESPONSE" = "404" ]; then
    echo -e "${GREEN}✅ Protected endpoint secured (HTTP $PROTECTED_RESPONSE)${NC}"
else
    echo -e "${RED}❌ Protected endpoint not secured (HTTP $PROTECTED_RESPONSE)${NC}"
    exit 1
fi

# Test direct Cloud Run access (should be blocked)
echo "Testing direct Cloud Run access (should be blocked)..."
DIRECT_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "https://legal-api-stg-gfunh6mfpa-uc.a.run.app/health")
if [ "$DIRECT_RESPONSE" = "403" ]; then
    echo -e "${GREEN}✅ Direct access properly blocked (403)${NC}"
else
    echo -e "${RED}❌ Direct access not blocked (HTTP $DIRECT_RESPONSE)${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Initial health checks passed!${NC}"
echo ""

# Create soak test log file
SOAK_LOG="soak_test_$(date +%Y%m%d_%H%M%S).log"
echo "Soak test started at $(date)" > "$SOAK_LOG"
echo "Gateway URL: $GATEWAY_URL" >> "$SOAK_LOG"
echo "Expected duration: 48 hours" >> "$SOAK_LOG"
echo "Success criteria:" >> "$SOAK_LOG"
echo "- 2 successful nightly authority jobs" >> "$SOAK_LOG"
echo "- P95 response time < 800ms" >> "$SOAK_LOG"
echo "- 99.9%+ uptime" >> "$SOAK_LOG"
echo "- Zero security incidents" >> "$SOAK_LOG"
echo "" >> "$SOAK_LOG"

# Create monitoring script
cat > scripts/monitor_soak_test.sh << 'EOF'
#!/bin/bash
# Continuous monitoring script for soak test

GATEWAY_URL="https://legal-database-gateway-1k6gjpoj.uc.gateway.dev"
LOG_FILE="soak_test_monitoring.log"

while true; do
    TIMESTAMP=$(date)
    
    # Test health endpoint
    HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}:%{time_total}" "$GATEWAY_URL/health")
    HTTP_CODE=$(echo $HEALTH_STATUS | cut -d: -f1)
    RESPONSE_TIME=$(echo $HEALTH_STATUS | cut -d: -f2)
    
    # Log results
    echo "$TIMESTAMP - Health: $HTTP_CODE, Response: ${RESPONSE_TIME}s" >> "$LOG_FILE"
    
    # Test sample endpoint
    SAMPLE_STATUS=$(curl -s -o /dev/null -w "%{http_code}:%{time_total}" "$GATEWAY_URL/v0/graph/sample")
    SAMPLE_CODE=$(echo $SAMPLE_STATUS | cut -d: -f1)
    SAMPLE_TIME=$(echo $SAMPLE_STATUS | cut -d: -f2)
    
    echo "$TIMESTAMP - Sample: $SAMPLE_CODE, Response: ${SAMPLE_TIME}s" >> "$LOG_FILE"
    
    # Check if response time is acceptable (< 2 seconds for warning)
    if (( $(echo "$RESPONSE_TIME > 2.0" | bc -l) )); then
        echo "$TIMESTAMP - WARNING: High response time: ${RESPONSE_TIME}s" >> "$LOG_FILE"
    fi
    
    # Sleep for 5 minutes
    sleep 300
done
EOF

chmod +x scripts/monitor_soak_test.sh

# Create validation script
cat > scripts/validate_soak_test.sh << 'EOF'
#!/bin/bash
# Validate soak test results after 48 hours

echo "🧪 Soak Test Validation Report"
echo "=============================="
echo "Generated: $(date)"
echo ""

GATEWAY_URL="https://legal-database-gateway-1k6gjpoj.uc.gateway.dev"

echo "📊 Final System Health Check:"
echo "=============================="

# Final health check
FINAL_HEALTH=$(curl -s -o /dev/null -w "%{http_code}:%{time_total}" "$GATEWAY_URL/health")
HTTP_CODE=$(echo $FINAL_HEALTH | cut -d: -f1)
RESPONSE_TIME=$(echo $FINAL_HEALTH | cut -d: -f2)

echo "Health endpoint: HTTP $HTTP_CODE, Response time: ${RESPONSE_TIME}s"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Health check: PASS"
else
    echo "❌ Health check: FAIL"
fi

if (( $(echo "$RESPONSE_TIME < 1.0" | bc -l) )); then
    echo "✅ Response time: PASS (< 1s)"
else
    echo "⚠️  Response time: WARNING (${RESPONSE_TIME}s)"
fi

echo ""
echo "🔒 Security Validation:"
echo "======================"

# Test security
PROTECTED_TEST=$(curl -s -o /dev/null -w "%{http_code}" "$GATEWAY_URL/v0/search?q=test")
DIRECT_TEST=$(curl -s -o /dev/null -w "%{http_code}" "https://legal-api-stg-gfunh6mfpa-uc.a.run.app/health")

echo "Protected endpoint (should be 401/404): HTTP $PROTECTED_TEST"
echo "Direct access (should be 403): HTTP $DIRECT_TEST"

if [ "$PROTECTED_TEST" = "401" ] || [ "$PROTECTED_TEST" = "404" ]; then
    echo "✅ Protected endpoint security: PASS"
else
    echo "❌ Protected endpoint security: FAIL"
fi

if [ "$DIRECT_TEST" = "403" ]; then
    echo "✅ Direct access blocking: PASS"
else
    echo "❌ Direct access blocking: FAIL"
fi

echo ""
echo "📈 Monitoring Data Analysis:"
echo "============================"
echo "Check soak_test_monitoring.log for detailed metrics"
echo "Look for any WARNING entries or failed requests"

echo ""
echo "🎯 Authority Job Status:"
echo "======================="
echo "Check Cloud Scheduler logs for nightly authority job execution"
echo "Expected: 2 successful runs during 48h period"

echo ""
echo "✅ Soak test validation complete"
echo ""
echo "🚀 If all checks pass, proceed with production deployment:"
echo "   ./scripts/deploy_production.sh"
EOF

chmod +x scripts/validate_soak_test.sh

echo ""
echo -e "${BLUE}📋 Soak Test Setup Complete${NC}"
echo "=========================="
echo "🕐 Duration: 48 hours"
echo "📊 Monitoring: Continuous health checks every 5 minutes"
echo "📝 Logs: soak_test_monitoring.log"
echo "🔍 Validation: Run ./scripts/validate_soak_test.sh after 48h"
echo ""
echo -e "${YELLOW}📅 Key Milestones:${NC}"
echo "• Hour 24: First nightly authority job (2 AM UTC)"
echo "• Hour 48: Second nightly authority job (2 AM UTC)"
echo "• Hour 48: Run validation script"
echo ""
echo -e "${GREEN}🎯 Success Criteria:${NC}"
echo "• All health checks passing"
echo "• Response times < 1 second"
echo "• Security controls working"
echo "• 2 successful authority jobs"
echo "• No service interruptions"
echo ""
echo -e "${BLUE}🚀 Starting continuous monitoring...${NC}"

# Start background monitoring
nohup ./scripts/monitor_soak_test.sh > /dev/null 2>&1 &
MONITOR_PID=$!

echo "Monitor process started with PID: $MONITOR_PID"
echo "Monitor PID: $MONITOR_PID" >> "$SOAK_LOG"
echo ""
echo -e "${GREEN}✅ 48-hour soak test is now running!${NC}"
echo ""
echo -e "${YELLOW}📋 What to do next:${NC}"
echo "1. Monitor logs: tail -f soak_test_monitoring.log"
echo "2. Check Cloud Monitoring: https://console.cloud.google.com/monitoring"
echo "3. Wait 48 hours"
echo "4. Run validation: ./scripts/validate_soak_test.sh"
echo "5. If successful, deploy to production: ./scripts/deploy_production.sh"
echo ""
echo -e "${BLUE}🎉 Soak test initiated successfully!${NC}"
