#!/bin/bash
# Production Deployment Script - Week 6 to Production
# Deploy after successful 48h soak test

set -e

# Configuration
PROJECT_ID="${PROJECT_ID:-texas-laws-personalinjury}"
REGION="${REGION:-us-central1}"
PROD_SERVICE_NAME="legal-api-prod"
STAGING_SERVICE_NAME="legal-api-stg"
IMAGE_TAG="week6"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Production Deployment - Week 6${NC}"
echo "=================================="
echo "Target: $PROD_SERVICE_NAME"
echo "Image: gcr.io/$PROJECT_ID/legal-api:$IMAGE_TAG"
echo "Region: $REGION"
echo ""

# Pre-deployment validation
echo -e "${YELLOW}🔍 Pre-deployment validation...${NC}"

# Check if soak test passed
echo "Checking soak test results..."
SOAK_TEST_STATUS=$(curl -s "$GATEWAY_URL/soak-test-report" | jq -r '.summary.overall_status' || echo "UNKNOWN")

if [ "$SOAK_TEST_STATUS" != "PASS" ]; then
    echo -e "${RED}❌ Soak test did not pass. Status: $SOAK_TEST_STATUS${NC}"
    echo "Please ensure 48h soak test completes successfully before production deployment."
    exit 1
fi

echo -e "${GREEN}✅ Soak test passed${NC}"

# Verify staging service health
echo "Verifying staging service health..."
STAGING_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" "https://legal-database-gateway-1k6gjpoj.uc.gateway.dev/health")

if [ "$STAGING_HEALTH" != "200" ]; then
    echo -e "${RED}❌ Staging service unhealthy: HTTP $STAGING_HEALTH${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Staging service healthy${NC}"

# Check image exists
echo "Verifying production image..."
if ! gcloud container images describe "gcr.io/$PROJECT_ID/legal-api:$IMAGE_TAG" >/dev/null 2>&1; then
    echo -e "${RED}❌ Production image not found: gcr.io/$PROJECT_ID/legal-api:$IMAGE_TAG${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Production image verified${NC}"

# Production deployment
echo -e "${YELLOW}🚀 Deploying to production...${NC}"

# Deploy Cloud Run service
gcloud run deploy $PROD_SERVICE_NAME \
    --image "gcr.io/$PROJECT_ID/legal-api:$IMAGE_TAG" \
    --region $REGION \
    --memory 2Gi \
    --max-instances 10 \
    --min-instances 1 \
    --no-allow-unauthenticated \
    --set-env-vars "CACHE_BACKEND=memory" \
    --set-env-vars "RATE_LIMIT_BACKEND=memory" \
    --set-env-vars "SUPABASE_JWKS_URL=https://anwefmklplkjxkmzpnva.supabase.co/auth/v1/keys" \
    --set-env-vars "LOG_LEVEL=INFO" \
    --set-env-vars "ENVIRONMENT=production" \
    --platform managed \
    --port 8000 \
    --timeout 300 \
    --concurrency 100 \
    --cpu 2 \
    --tag "week6-$(date +%Y%m%d-%H%M%S)"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Production service deployed${NC}"
else
    echo -e "${RED}❌ Production deployment failed${NC}"
    exit 1
fi

# Get production service URL
PROD_URL=$(gcloud run services describe $PROD_SERVICE_NAME --region $REGION --format='value(status.url)')
echo "Production URL: $PROD_URL"

# Grant API Gateway access to production service
echo -e "${YELLOW}🔑 Configuring production access...${NC}"

# Grant the API Gateway service accounts access to production
gcloud run services add-iam-policy-binding $PROD_SERVICE_NAME \
    --region=$REGION \
    --member="serviceAccount:api-gateway-invoker@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/run.invoker"

gcloud run services add-iam-policy-binding $PROD_SERVICE_NAME \
    --region=$REGION \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/run.invoker"

echo -e "${GREEN}✅ Production access configured${NC}"

# Create production API Gateway configuration
echo -e "${YELLOW}🌐 Setting up production API Gateway...${NC}"

# Update gateway config for production
sed "s|legal-api-stg-gfunh6mfpa-uc.a.run.app|${PROD_URL#https://}|g" gateway/api-config.yaml > gateway/api-config-prod.yaml

# Create production API Gateway API
gcloud api-gateway apis create legal-database-prod \
    --display-name="Legal Database API - Production" || echo "API already exists"

# Create production API config
gcloud api-gateway api-configs create v1-production \
    --api=legal-database-prod \
    --openapi-spec=gateway/api-config-prod.yaml

# Create production gateway
gcloud api-gateway gateways create legal-database-prod-gateway \
    --api=legal-database-prod \
    --api-config=v1-production \
    --location=$REGION \
    --display-name="Legal Database Production Gateway" || echo "Gateway already exists"

# Get production gateway URL
PROD_GATEWAY_URL=$(gcloud api-gateway gateways describe legal-database-prod-gateway --location=$REGION --format='value(defaultHostname)')

echo -e "${GREEN}✅ Production API Gateway configured${NC}"
echo "Production Gateway URL: https://$PROD_GATEWAY_URL"

# Production smoke tests
echo -e "${YELLOW}🧪 Running production smoke tests...${NC}"

# Test health endpoint
PROD_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" "https://$PROD_GATEWAY_URL/health")
if [ "$PROD_HEALTH" = "200" ]; then
    echo -e "${GREEN}✅ Production health check passed${NC}"
else
    echo -e "${RED}❌ Production health check failed: HTTP $PROD_HEALTH${NC}"
fi

# Test sample graph endpoint
PROD_SAMPLE=$(curl -s -o /dev/null -w "%{http_code}" "https://$PROD_GATEWAY_URL/v0/graph/sample")
if [ "$PROD_SAMPLE" = "200" ]; then
    echo -e "${GREEN}✅ Production sample endpoint working${NC}"
else
    echo -e "${RED}❌ Production sample endpoint failed: HTTP $PROD_SAMPLE${NC}"
fi

# Test protected endpoint (should be 401/404 without auth)
PROD_SEARCH=$(curl -s -o /dev/null -w "%{http_code}" "https://$PROD_GATEWAY_URL/v0/search?q=test")
if [ "$PROD_SEARCH" = "401" ] || [ "$PROD_SEARCH" = "404" ]; then
    echo -e "${GREEN}✅ Production auth protection working${NC}"
else
    echo -e "${RED}❌ Production auth protection failed: HTTP $PROD_SEARCH${NC}"
fi

# Set up production monitoring
echo -e "${YELLOW}📊 Configuring production monitoring...${NC}"

# Create production alerting policy
cat > monitoring/production-alerts.yaml << 'EOF'
groups:
  - name: production-critical
    rules:
      - alert: ProductionServiceDown
        expr: up{job="legal-api-prod"} == 0
        for: 1m
        labels:
          severity: critical
          environment: production
        annotations:
          summary: "Production service is down"
          description: "Legal API production service has been down for more than 1 minute."

      - alert: ProductionHighErrorRate
        expr: rate(http_requests_total{job="legal-api-prod",status=~"5.."}[5m]) / rate(http_requests_total{job="legal-api-prod"}[5m]) > 0.01
        for: 2m
        labels:
          severity: critical
          environment: production
        annotations:
          summary: "High error rate in production"
          description: "Production error rate is {{ $value | humanizePercentage }} over the last 5 minutes."

      - alert: ProductionHighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="legal-api-prod"}[5m])) > 1.0
        for: 5m
        labels:
          severity: warning
          environment: production
        annotations:
          summary: "High latency in production"
          description: "Production P95 latency is {{ $value }}s over the last 5 minutes."
EOF

echo -e "${GREEN}✅ Production monitoring configured${NC}"

# Create production deployment summary
echo ""
echo -e "${BLUE}📋 Production Deployment Summary${NC}"
echo "================================="
echo "🌐 Production Service: $PROD_URL"
echo "🌐 Production Gateway: https://$PROD_GATEWAY_URL"
echo "🏷️  Image Tag: $IMAGE_TAG"
echo "📍 Region: $REGION"
echo "💾 Memory: 2Gi"
echo "🔄 Instances: 1-10 (auto-scaling)"
echo "🔒 Security: Hyper-secure (SA-only access)"
echo ""
echo -e "${GREEN}🎉 Production deployment successful!${NC}"
echo ""
echo -e "${YELLOW}📝 Next Steps:${NC}"
echo "1. Update AiLex Core env-var to: https://$PROD_GATEWAY_URL"
echo "2. Monitor production metrics for 24h"
echo "3. Proceed with Week 7 Redis cache implementation"
echo "4. Tag v1.0.0 for OpenAPI freeze"
echo ""
echo -e "${BLUE}🔗 Production URLs:${NC}"
echo "• Health: https://$PROD_GATEWAY_URL/health"
echo "• Sample Graph: https://$PROD_GATEWAY_URL/v0/graph/sample"
echo "• Search (auth required): https://$PROD_GATEWAY_URL/v0/search"
echo "• Graph (auth required): https://$PROD_GATEWAY_URL/v0/graph"
echo ""
echo -e "${GREEN}🚀 Production is live and ready for traffic!${NC}"
