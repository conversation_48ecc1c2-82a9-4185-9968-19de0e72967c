#!/bin/bash
# Validate soak test results after 48 hours

echo "🧪 Soak Test Validation Report"
echo "=============================="
echo "Generated: $(date)"
echo ""

GATEWAY_URL="https://legal-database-gateway-1k6gjpoj.uc.gateway.dev"

echo "📊 Final System Health Check:"
echo "=============================="

# Final health check
FINAL_HEALTH=$(curl -s -o /dev/null -w "%{http_code}:%{time_total}" "$GATEWAY_URL/health")
HTTP_CODE=$(echo $FINAL_HEALTH | cut -d: -f1)
RESPONSE_TIME=$(echo $FINAL_HEALTH | cut -d: -f2)

echo "Health endpoint: HTTP $HTTP_CODE, Response time: ${RESPONSE_TIME}s"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Health check: PASS"
else
    echo "❌ Health check: FAIL"
fi

if (( $(echo "$RESPONSE_TIME < 1.0" | bc -l) )); then
    echo "✅ Response time: PASS (< 1s)"
else
    echo "⚠️  Response time: WARNING (${RESPONSE_TIME}s)"
fi

echo ""
echo "🔒 Security Validation:"
echo "======================"

# Test security
PROTECTED_TEST=$(curl -s -o /dev/null -w "%{http_code}" "$GATEWAY_URL/v0/search?q=test")
DIRECT_TEST=$(curl -s -o /dev/null -w "%{http_code}" "https://legal-api-stg-gfunh6mfpa-uc.a.run.app/health")

echo "Protected endpoint (should be 401/404): HTTP $PROTECTED_TEST"
echo "Direct access (should be 403): HTTP $DIRECT_TEST"

if [ "$PROTECTED_TEST" = "401" ] || [ "$PROTECTED_TEST" = "404" ]; then
    echo "✅ Protected endpoint security: PASS"
else
    echo "❌ Protected endpoint security: FAIL"
fi

if [ "$DIRECT_TEST" = "403" ]; then
    echo "✅ Direct access blocking: PASS"
else
    echo "❌ Direct access blocking: FAIL"
fi

echo ""
echo "📈 Monitoring Data Analysis:"
echo "============================"
echo "Check soak_test_monitoring.log for detailed metrics"
echo "Look for any WARNING entries or failed requests"

echo ""
echo "🎯 Authority Job Status:"
echo "======================="
echo "Check Cloud Scheduler logs for nightly authority job execution"
echo "Expected: 2 successful runs during 48h period"

echo ""
echo "✅ Soak test validation complete"
echo ""
echo "🚀 If all checks pass, proceed with production deployment:"
echo "   ./scripts/deploy_production.sh"
