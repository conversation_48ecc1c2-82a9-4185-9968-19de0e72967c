#!/bin/bash
# Hyper-Secure Deployment Script - Lock down Cloud Run and implement proper security

set -e  # Exit on any error

# Configuration
PROJECT_ID="${PROJECT_ID:-texas-laws-personalinjury}"
REGION="${REGION:-us-central1}"
SERVICE_NAME="legal-api-stg"
GATEWAY_NAME="legal-database-gateway"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔒 Implementing Hyper-Secure Architecture${NC}"
echo "=============================================="

# Step 1: Create dedicated service account for API Gateway
echo -e "${YELLOW}🔧 Creating API Gateway service account...${NC}"
SA_NAME="api-gateway-invoker"
SA_EMAIL="${SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

# Check if service account exists
if gcloud iam service-accounts describe $SA_EMAIL >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Service account $SA_EMAIL already exists${NC}"
else
    gcloud iam service-accounts create $SA_NAME \
        --display-name="API Gateway Cloud Run Invoker" \
        --description="Service account for API Gateway to invoke Cloud Run services"
    echo -e "${GREEN}✅ Created service account: $SA_EMAIL${NC}"
fi

# Step 2: Remove public access from Cloud Run
echo -e "${YELLOW}🔒 Removing public access from Cloud Run...${NC}"
gcloud run services remove-iam-policy-binding $SERVICE_NAME \
    --region=$REGION \
    --member="allUsers" \
    --role="roles/run.invoker" \
    --quiet || echo "No public access to remove"

# Step 3: Grant only the API Gateway service account access
echo -e "${YELLOW}🔑 Granting API Gateway service account access...${NC}"
gcloud run services add-iam-policy-binding $SERVICE_NAME \
    --region=$REGION \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/run.invoker"

echo -e "${GREEN}✅ Cloud Run is now locked down to API Gateway only${NC}"

# Step 4: Update API Gateway to use the service account
echo -e "${YELLOW}🔧 Updating API Gateway configuration...${NC}"

# Create new API config with backend authentication
gcloud api-gateway api-configs create v3-secure \
    --api=legal-database \
    --openapi-spec=gateway/api-config.yaml \
    --backend-auth-service-account=$SA_EMAIL

# Update gateway to use new config
gcloud api-gateway gateways update $GATEWAY_NAME \
    --api-config=v3-secure \
    --location=$REGION

echo -e "${GREEN}✅ API Gateway updated with secure backend authentication${NC}"

# Step 5: Verify security
echo -e "${YELLOW}🧪 Testing security implementation...${NC}"

# Get service URLs
CLOUD_RUN_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(status.url)')
GATEWAY_URL=$(gcloud api-gateway gateways describe $GATEWAY_NAME --location=$REGION --format='value(defaultHostname)')

echo "Cloud Run URL: $CLOUD_RUN_URL"
echo "Gateway URL: https://$GATEWAY_URL"

# Test 1: Direct Cloud Run access should be blocked
echo -e "${YELLOW}Testing direct Cloud Run access (should be 403)...${NC}"
DIRECT_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$CLOUD_RUN_URL/health")
if [ "$DIRECT_RESPONSE" = "403" ]; then
    echo -e "${GREEN}✅ Direct access properly blocked (403)${NC}"
else
    echo -e "${RED}❌ Direct access not blocked (HTTP $DIRECT_RESPONSE)${NC}"
fi

# Test 2: Gateway public endpoints should work
echo -e "${YELLOW}Testing gateway public endpoints...${NC}"
GATEWAY_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" "https://$GATEWAY_URL/health")
if [ "$GATEWAY_HEALTH" = "200" ]; then
    echo -e "${GREEN}✅ Gateway health endpoint working (200)${NC}"
else
    echo -e "${RED}❌ Gateway health endpoint failed (HTTP $GATEWAY_HEALTH)${NC}"
fi

# Test 3: Gateway protected endpoints should require auth
echo -e "${YELLOW}Testing gateway protected endpoints (should be 401)...${NC}"
GATEWAY_SEARCH=$(curl -s -o /dev/null -w "%{http_code}" "https://$GATEWAY_URL/v0/search?q=test")
if [ "$GATEWAY_SEARCH" = "401" ]; then
    echo -e "${GREEN}✅ Protected endpoints require auth (401)${NC}"
else
    echo -e "${RED}❌ Protected endpoints not secured (HTTP $GATEWAY_SEARCH)${NC}"
fi

echo ""
echo -e "${BLUE}🔒 Security Implementation Summary${NC}"
echo "=================================="
echo "✅ Cloud Run locked down to API Gateway service account only"
echo "✅ Direct Cloud Run access blocked (403 Forbidden)"
echo "✅ API Gateway handles all authentication"
echo "✅ Public endpoints accessible via gateway"
echo "✅ Protected endpoints require JWT via gateway"
echo ""
echo -e "${GREEN}🎉 Hyper-secure architecture implemented!${NC}"
echo ""
echo -e "${BLUE}📋 Security Architecture${NC}"
echo "======================="
echo "🌐 Public Access: https://$GATEWAY_URL (API Gateway only)"
echo "🔒 Direct Access: $CLOUD_RUN_URL (BLOCKED)"
echo "🔑 Authentication: JWT via API Gateway"
echo "🛡️  Authorization: FastAPI RBAC + JWT middleware"
echo ""
echo -e "${YELLOW}⚠️  Important Security Notes:${NC}"
echo "• Only the API Gateway can reach Cloud Run"
echo "• All requests must go through the gateway"
echo "• JWT validation happens at both gateway and FastAPI levels"
echo "• Rate limiting and RBAC enforced in FastAPI"
echo "• No direct Cloud Run access possible"
